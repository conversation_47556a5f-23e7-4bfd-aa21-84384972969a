import React, { useState, useRef, useEffect } from 'react';

const TutorialModal = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('usage');
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [formStatus, setFormStatus] = useState({
    submitting: false,
    success: false,
    error: false,
    message: ''
  });
  const modalRef = useRef(null);

  // Handle click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      // Prevent scrolling when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      // Restore scrolling when modal is closed
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  // State for tracking modal closing animation
  const [isClosing, setIsClosing] = useState(false);

  // Handle modal close with animation
  const handleClose = () => {
    setIsClosing(true);
    if (modalRef.current) {
      setTimeout(() => {
        onClose();
        setIsClosing(false);
      }, 300); // Match this with CSS transition duration
    } else {
      onClose();
    }
  };

  // Handle contact form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setContactForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle contact form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!contactForm.name || !contactForm.email || !contactForm.message) {
      setFormStatus({
        submitting: false,
        success: false,
        error: true,
        message: 'Please fill in all fields'
      });
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(contactForm.email)) {
      setFormStatus({
        submitting: false,
        success: false,
        error: true,
        message: 'Please enter a valid email address'
      });
      return;
    }

    // Simulate form submission
    setFormStatus({
      submitting: true,
      success: false,
      error: false,
      message: ''
    });

    // Simulate API call
    setTimeout(() => {
      setFormStatus({
        submitting: false,
        success: true,
        error: false,
        message: 'Thank you for your message! We will get back to you soon.'
      });

      // Reset form
      setContactForm({
        name: '',
        email: '',
        message: ''
      });

      // Reset success message after 5 seconds
      setTimeout(() => {
        setFormStatus(prev => ({
          ...prev,
          success: false,
          message: ''
        }));
      }, 5000);
    }, 1500);
  };



  if (!isOpen) return null;

  return (
    <div className="modal-overlay center-fix">
      <div
        className={`modal-container tutorial-modal ${isClosing ? 'modal-closing' : 'modal-opening'}`}
        ref={modalRef}
      >
        <div className="modal-header">
          <h2>VertoAI Help Center</h2>
          <button className="modal-close" onClick={handleClose}>×</button>
        </div>

        <div className="modal-tabs">
          <button
            className={`modal-tab ${activeTab === 'usage' ? 'active' : ''}`}
            onClick={() => setActiveTab('usage')}
          >
            How to Use
          </button>
          <button
            className={`modal-tab ${activeTab === 'concepts' ? 'active' : ''}`}
            onClick={() => setActiveTab('concepts')}
          >
            Key Concepts
          </button>
          <button
            className={`modal-tab ${activeTab === 'support' ? 'active' : ''}`}
            onClick={() => setActiveTab('support')}
          >
            Help & Support
          </button>
          <button
            className={`modal-tab ${activeTab === 'contact' ? 'active' : ''}`}
            onClick={() => setActiveTab('contact')}
          >
            Contact Us
          </button>
        </div>

        <div className="modal-body">
          {activeTab === 'usage' && (
            <div className="tutorial-content">
              <h3>Getting Started with VertoAI</h3>

              <div className="tutorial-section">
                <h4>1. Select a Domain</h4>
                <p>Choose the specific domain you want to work with from the dropdown menu:</p>
                <ul>
                  <li><strong>Fintech:</strong> Financial regulations, risk assessment, and investment analysis</li>
                  <li><strong>Healthcare:</strong> Medical diagnostics, treatment recommendations, and health records</li>
                  <li><strong>Education:</strong> Curriculum development, student assessment, and educational content</li>
                  <li><strong>Law:</strong> Legal research, contract analysis, and case summarization</li>
                </ul>
              </div>

              <div className="tutorial-section">
                <h4>2. Choose an LLM Provider</h4>
                <p>Select which AI model provider to use:</p>
                <ul>
                  <li><strong>OpenAI:</strong> GPT models (GPT-3.5, GPT-4)</li>
                  <li><strong>Gemini:</strong> Google's advanced AI models</li>
                  <li><strong>Claude:</strong> Anthropic's helpful, harmless, and honest AI</li>
                  <li><strong>OpenRouter:</strong> Access multiple providers through one API</li>
                  <li><strong>DeepSeek:</strong> Specialized in code and technical content</li>
                </ul>
              </div>

              <div className="tutorial-section">
                <h4>3. Enter Your API Key</h4>
                <p>Click the key icon in the top right to enter your API key for the selected provider.</p>
                <p>Your API key is never stored on our servers and is only passed through to the provider.</p>
              </div>

              <div className="tutorial-section">
                <h4>4. Start Chatting</h4>
                <p>Type your message in the chat input and press Enter or click Send.</p>
                <p>The AI will respond based on your selected domain and provider.</p>
              </div>
            </div>
          )}

          {activeTab === 'concepts' && (
            <div className="tutorial-content">
              <h3>Key AI Concepts</h3>

              <div className="tutorial-section">
                <h4>What is LoRA?</h4>
                <p>Low-Rank Adaptation (LoRA) is a technique that efficiently fine-tunes large language models by training only a small set of parameters instead of the entire model.</p>
                <p>Benefits include:</p>
                <ul>
                  <li>Faster training times</li>
                  <li>Lower computational requirements</li>
                  <li>Smaller model file sizes</li>
                </ul>
              </div>

              <div className="tutorial-section">
                <h4>What is QLoRA?</h4>
                <p>Quantized Low-Rank Adaptation (QLoRA) combines quantization (reducing model precision to 4-bit) with LoRA for even more efficient fine-tuning.</p>
                <p>This allows fine-tuning of very large models on consumer hardware.</p>
              </div>

              <div className="tutorial-section">
                <h4>What are Adapters?</h4>
                <p>Adapters are small, trainable modules that are inserted between layers of a pre-trained model. They allow for efficient fine-tuning without modifying the original model weights.</p>
              </div>

              <div className="tutorial-section">
                <h4>What is Domain-Specific Fine-Tuning?</h4>
                <p>Domain-specific fine-tuning adapts a general-purpose AI model to perform better on tasks within a specific field (like finance or healthcare).</p>
                <p>This results in more accurate, relevant, and specialized responses for your domain.</p>
              </div>
            </div>
          )}

          {activeTab === 'support' && (
            <div className="tutorial-content">
              <h3>Help & Support</h3>

              <div className="tutorial-section">
                <h4>Common Issues</h4>
                <ul>
                  <li><strong>API Key Errors:</strong> Ensure you've entered the correct API key for your selected provider.</li>
                  <li><strong>Rate Limiting:</strong> Most providers have usage limits. If you encounter errors, you may have reached your limit.</li>
                  <li><strong>Model Availability:</strong> Some models may be temporarily unavailable from certain providers.</li>
                </ul>
              </div>

              <div className="tutorial-section">
                <h4>Getting Help</h4>
                <p>For additional support:</p>
                <ul>
                  <li>Visit our <a href="https://github.com/yourusername/vertoai" target="_blank" rel="noopener noreferrer">GitHub repository</a></li>
                  <li>Join our <a href="#" target="_blank" rel="noopener noreferrer">Discord community</a></li>
                  <li>Email us at <a href="mailto:<EMAIL>"><EMAIL></a></li>
                </ul>
              </div>

              <div className="tutorial-section">
                <h4>Contributing</h4>
                <p>VertoAI is open-source! We welcome contributions from the community:</p>
                <ul>
                  <li>Report bugs and suggest features on GitHub</li>
                  <li>Submit pull requests to improve the codebase</li>
                  <li>Help with documentation and tutorials</li>
                </ul>
              </div>

              <div className="tutorial-section">
                <h4>Free Usage</h4>
                <p>You can use VertoAI without an API key for up to 10 prompts per day. After that, you'll need to provide your own API key or wait for the daily limit to reset.</p>
              </div>
            </div>
          )}

          {activeTab === 'contact' && (
            <div className="tutorial-content">
              <h3>Contact Us</h3>
              <p>Have questions, feedback, or need support? Reach out to us:</p>

              <div className="contact-info">
                <div className="contact-item">
                  <h4>Email</h4>
                  <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>

                <div className="contact-item">
                  <h4>Twitter</h4>
                  <p><a href="https://twitter.com/vertoai" target="_blank" rel="noopener noreferrer">@vertoai</a></p>
                </div>

                <div className="contact-item">
                  <h4>GitHub</h4>
                  <p><a href="https://github.com/vertoai/vertocore" target="_blank" rel="noopener noreferrer">github.com/vertoai/vertocore</a></p>
                </div>
              </div>

              <div className="contact-form-container">
                <h4>Send us a message</h4>
                <p className="contact-form-intro">We'd love to hear your feedback, questions, or suggestions about VertoAI. Fill out the form below and we'll get back to you as soon as possible.</p>

                <form className="contact-form" onSubmit={handleSubmit}>
                  <div className="form-group">
                    <label htmlFor="name">Name</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={contactForm.name}
                      onChange={handleInputChange}
                      placeholder="Your name"
                      className={formStatus.error && !contactForm.name ? 'error' : ''}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="email">Email</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={contactForm.email}
                      onChange={handleInputChange}
                      placeholder="Your email address"
                      className={formStatus.error && (!contactForm.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contactForm.email)) ? 'error' : ''}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="subject">Subject</label>
                    <select
                      id="subject"
                      name="subject"
                      onChange={handleInputChange}
                      defaultValue=""
                    >
                      <option value="" disabled>Select a subject</option>
                      <option value="feedback">Feedback</option>
                      <option value="question">Question</option>
                      <option value="bug">Bug Report</option>
                      <option value="feature">Feature Request</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label htmlFor="message">Message</label>
                    <textarea
                      id="message"
                      name="message"
                      value={contactForm.message}
                      onChange={handleInputChange}
                      placeholder="Your message"
                      rows="4"
                      className={formStatus.error && !contactForm.message ? 'error' : ''}
                      required
                    ></textarea>
                  </div>

                  {formStatus.error && (
                    <div className="form-error">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                      </svg>
                      <span>{formStatus.message}</span>
                    </div>
                  )}

                  {formStatus.success && (
                    <div className="form-success">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <span>{formStatus.message}</span>
                    </div>
                  )}

                  <div className="form-actions">
                    <button
                      type="button"
                      className="cancel-button"
                      onClick={() => {
                        setContactForm({
                          name: '',
                          email: '',
                          message: ''
                        });
                        setFormStatus({
                          submitting: false,
                          success: false,
                          error: false,
                          message: ''
                        });
                      }}
                    >
                      Clear Form
                    </button>

                    <button
                      type="submit"
                      className="submit-button"
                      disabled={formStatus.submitting}
                    >
                      {formStatus.submitting ? (
                        <div className="loading-dots">
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                      ) : 'Send Message'}
                    </button>
                  </div>
                </form>

                <div className="privacy-note">
                  <p>By submitting this form, you agree to our <a href="#" onClick={(e) => e.preventDefault()}>Privacy Policy</a>. We'll never share your information with third parties.</p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button className="modal-button primary" onClick={handleClose}>Close</button>
        </div>
      </div>
    </div>
  );
};

export default TutorialModal;
