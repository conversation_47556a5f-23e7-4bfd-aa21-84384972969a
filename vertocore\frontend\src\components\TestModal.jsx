import React from 'react';

const TestModal = ({ isOpen, onClose }) => {
  console.log('TestModal rendered with isOpen:', isOpen);
  
  if (!isOpen) {
    console.log('TestModal: not open, returning null');
    return null;
  }
  
  console.log('TestModal: should be visible!');
  
  return (
    <div 
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 0, 0, 0.9)',
        zIndex: 999999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '24px',
        color: 'white',
        fontWeight: 'bold'
      }}
      onClick={onClose}
    >
      <div 
        style={{
          backgroundColor: 'white',
          color: 'black',
          padding: '2rem',
          borderRadius: '12px',
          textAlign: 'center',
          maxWidth: '400px'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <h2>TEST MODAL</h2>
        <p>If you can see this, the modal system is working!</p>
        <button 
          onClick={onClose}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default TestModal;
