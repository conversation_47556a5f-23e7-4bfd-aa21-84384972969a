import React, { useState, useRef, useEffect } from 'react';

const Dropdown = ({
  options,
  selectedValue,
  onSelect,
  placeholder = 'Select an option'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dropDirection, setDropDirection] = useState('down');
  const dropdownRef = useRef(null);
  const menuRef = useRef(null);

  // Find the selected option
  const selectedOption = options.find(option => option.id === selectedValue);

  // Calculate optimal dropdown direction
  const calculateDropDirection = () => {
    if (!dropdownRef.current) return 'down';

    const rect = dropdownRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    const menuHeight = 250; // Max height from CSS

    // If there's enough space below, drop down
    if (spaceBelow >= menuHeight) {
      return 'down';
    }
    // If there's more space above than below, drop up
    else if (spaceAbove > spaceBelow) {
      return 'up';
    }
    // Default to down
    else {
      return 'down';
    }
  };

  // Toggle dropdown
  const toggleDropdown = () => {
    if (!isOpen) {
      setDropDirection(calculateDropDirection());
    }
    setIsOpen(!isOpen);
  };

  // Handle option selection
  const handleSelect = (option) => {
    onSelect(option.id);
    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`dropdown-container ${isOpen ? 'dropdown-open' : ''} dropdown-${dropDirection}`} ref={dropdownRef}>
      <div className="dropdown-header" onClick={toggleDropdown}>
        <span className="dropdown-selected">
          {selectedOption ? (
            <>
              {selectedOption.color && (
                <span
                  className="dropdown-color-indicator"
                  style={{ backgroundColor: selectedOption.color }}
                ></span>
              )}
              {selectedOption.label}
            </>
          ) : placeholder}
        </span>
        <span className={`dropdown-icon ${dropDirection === 'up' ? 'dropdown-icon-up' : ''}`}>▼</span>
      </div>

      <div className="dropdown-menu" ref={menuRef}>
        {options.map(option => (
          <div
            key={option.id}
            className={`dropdown-item ${option.id === selectedValue ? 'active' : ''}`}
            onClick={() => handleSelect(option)}
          >
            {option.color && (
              <span
                className="dropdown-color-indicator"
                style={{ backgroundColor: option.color }}
              ></span>
            )}
            {option.label}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Dropdown;
