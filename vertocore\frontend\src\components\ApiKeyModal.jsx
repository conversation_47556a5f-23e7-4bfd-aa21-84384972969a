import React, { useState, useEffect, useRef } from 'react';
import Dropdown from './Dropdown';

const ApiKeyModal = ({ isOpen, onClose, onSave, selectedProvider, currentApiKey, onProviderChange }) => {
  const [apiKey, setApiKey] = useState('');
  const [showKey, setShowKey] = useState(false);
  const [provider, setProvider] = useState(selectedProvider);
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState('');
  const [isValid, setIsValid] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const modalRef = useRef(null);

  // Update API key and provider when they change or modal opens
  useEffect(() => {
    if (isOpen) {
      setApiKey(currentApiKey || '');
      setProvider(selectedProvider);
      setValidationError('');
      setIsValid(false);
      setIsClosing(false);
    }
  }, [isOpen, currentApiKey, selectedProvider]);

  // Handle modal close with animation
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      setIsClosing(false);
    }, 300); // Match this with CSS transition duration
  };

  // Handle click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Validate API key with more robust checks
  const validateApiKey = (key, providerType) => {
    setIsValidating(true);
    setValidationError('');

    // Simulate API validation with more thorough checks
    setTimeout(() => {
      let isKeyValid = false;
      let errorMessage = '';

      // Remove any whitespace from the key
      const trimmedKey = key.trim();

      if (trimmedKey.length === 0) {
        errorMessage = 'API key cannot be empty';
        setIsValid(false);
        setValidationError(errorMessage);
        setIsValidating(false);
        return;
      }

      // Check for common patterns that indicate invalid keys
      if (trimmedKey.includes('example') ||
          trimmedKey.includes('test') ||
          trimmedKey === 'your-api-key-here' ||
          /^[a-z]{5,10}$/.test(trimmedKey)) { // Simple gibberish check
        errorMessage = 'This appears to be a placeholder or test key, not a valid API key';
        setIsValid(false);
        setValidationError(errorMessage);
        setIsValidating(false);
        return;
      }

      switch (providerType) {
        case 'openai':
          // OpenAI keys are 51 characters and start with sk-
          isKeyValid = trimmedKey.startsWith('sk-') && trimmedKey.length >= 40;
          errorMessage = !isKeyValid ? 'OpenAI API keys should start with "sk-" and be at least 40 characters long' : '';
          break;
        case 'gemini':
          // Gemini keys are alphanumeric and typically longer than 20 chars
          isKeyValid = /^[A-Za-z0-9_-]{20,}$/.test(trimmedKey);
          errorMessage = !isKeyValid ? 'Gemini API keys should be at least 20 characters long and contain only letters, numbers, underscores, or hyphens' : '';
          break;
        case 'claude':
          // Claude keys start with sk-ant and are long
          isKeyValid = trimmedKey.startsWith('sk-ant-') && trimmedKey.length >= 30;
          errorMessage = !isKeyValid ? 'Claude API keys should start with "sk-ant-" and be at least 30 characters long' : '';
          break;
        case 'openrouter':
          // OpenRouter keys start with sk-or and are long
          isKeyValid = trimmedKey.startsWith('sk-or-') && trimmedKey.length >= 30;
          errorMessage = !isKeyValid ? 'OpenRouter API keys should start with "sk-or-" and be at least 30 characters long' : '';
          break;
        case 'deepseek':
          // DeepSeek keys are alphanumeric and typically longer than 20 chars
          isKeyValid = /^[A-Za-z0-9_-]{20,}$/.test(trimmedKey);
          errorMessage = !isKeyValid ? 'DeepSeek API keys should be at least 20 characters long and contain only letters, numbers, underscores, or hyphens' : '';
          break;
        default:
          // Generic check for any other provider
          isKeyValid = trimmedKey.length >= 20 && /^[A-Za-z0-9_\-\.]+$/.test(trimmedKey);
          errorMessage = !isKeyValid ? 'API key should be at least 20 characters long and contain valid characters' : '';
      }

      // In a real app, we would make an API call to validate the key
      // For now, we're just checking the format

      setIsValid(isKeyValid);
      setValidationError(errorMessage);
      setIsValidating(false);
    }, 500);
  };

  const handleKeyChange = (e) => {
    const newKey = e.target.value;
    setApiKey(newKey);

    if (newKey.length > 5) {
      validateApiKey(newKey, provider);
    } else {
      setIsValid(false);
      setValidationError('');
    }
  };

  const handleProviderChange = (newProvider) => {
    setProvider(newProvider);

    if (apiKey.length > 5) {
      validateApiKey(apiKey, newProvider);
    }
  };

  const handleSave = () => {
    if (isValid) {
      onSave(apiKey);
      if (provider !== selectedProvider) {
        onProviderChange(provider);
      }
      handleClose();
    } else {
      validateApiKey(apiKey, provider);
    }
  };

  const toggleShowKey = () => {
    setShowKey(!showKey);
  };

  // Get provider-specific placeholder
  const getPlaceholder = () => {
    switch (provider) {
      case 'openai':
        return 'Enter OpenAI API key (starts with sk-)';
      case 'gemini':
        return 'Enter Google Gemini API key';
      case 'claude':
        return 'Enter Anthropic Claude API key (starts with sk-ant-)';
      case 'openrouter':
        return 'Enter OpenRouter API key (starts with sk-or-)';
      case 'deepseek':
        return 'Enter DeepSeek API key';
      default:
        return 'Enter your API key (not stored)';
    }
  };

  // Get provider-specific info
  const getProviderInfo = () => {
    switch (provider) {
      case 'openai':
        return {
          name: 'OpenAI',
          description: 'Access GPT-3.5, GPT-4, and other OpenAI models.',
          url: 'https://platform.openai.com/api-keys',
          format: 'Starts with "sk-"'
        };
      case 'gemini':
        return {
          name: 'Google Gemini',
          description: 'Access Google\'s advanced AI models like Gemini Pro and Ultra.',
          url: 'https://ai.google.dev/',
          format: 'Alphanumeric string'
        };
      case 'claude':
        return {
          name: 'Anthropic Claude',
          description: 'Access Claude 3 models for helpful, harmless, and honest AI.',
          url: 'https://console.anthropic.com/account/keys',
          format: 'Starts with "sk-ant-"'
        };
      case 'openrouter':
        return {
          name: 'OpenRouter',
          description: 'Access Gemma 3 through OpenRouter. Free tier (10 prompts/day) is available, or add your own API key for 50+ prompts/day.',
          url: 'https://openrouter.ai/keys',
          format: 'Starts with "sk-or-"',
          hasFreeOption: true
        };
      case 'deepseek':
        return {
          name: 'DeepSeek',
          description: 'Access specialized code and technical content models.',
          url: 'https://platform.deepseek.com/api_keys',
          format: 'Alphanumeric string'
        };
      default:
        return {
          name: 'API Provider',
          description: 'Access AI models through your API key.',
          url: '#',
          format: 'Varies by provider'
        };
    }
  };

  const providerInfo = getProviderInfo();

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" style={{ zIndex: 10000 }}>
      <div
        className={`modal-container api-key-modal ${isClosing ? 'modal-closing' : 'modal-opening'}`}
        ref={modalRef}
        style={{ position: 'relative', zIndex: 10001 }}
      >
        <div className="modal-header">
          <h2>API Key Settings</h2>
          <button className="modal-close" onClick={handleClose}>×</button>
        </div>

        <div className="modal-body">
          <div className="provider-selector-container">
            <h3>Select Provider</h3>
            <Dropdown
              options={[
                { id: 'openai', label: 'OpenAI' },
                { id: 'gemini', label: 'Google Gemini' },
                { id: 'claude', label: 'Anthropic Claude' },
                { id: 'openrouter', label: 'OpenRouter' },
                { id: 'deepseek', label: 'DeepSeek' }
              ]}
              selectedValue={provider}
              onSelect={handleProviderChange}
              placeholder="Select a provider"
            />
          </div>

          <div className="provider-info">
            <p>{providerInfo.description}</p>
            <div className="provider-details">
              <div className="provider-detail">
                <span className="detail-label">Format:</span>
                <span className="detail-value">{providerInfo.format}</span>
              </div>
              <div className="provider-detail">
                <span className="detail-label">Get API Key:</span>
                <a
                  href={providerInfo.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="detail-link"
                >
                  {providerInfo.url}
                </a>
              </div>
            </div>
          </div>

          <div className="api-key-container modal-input-container">
            <input
              type={showKey ? "text" : "password"}
              placeholder={getPlaceholder()}
              className={`api-key-input modal-input ${validationError ? 'error' : isValid ? 'valid' : ''}`}
              value={apiKey}
              onChange={handleKeyChange}
            />
            <button
              className="api-key-toggle"
              onClick={toggleShowKey}
              title={showKey ? "Hide API key" : "Show API key"}
            >
              {showKey ? "🙈" : "👁️"}
            </button>
          </div>

          {isValidating && (
            <div className="validation-status validating">
              <div className="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <span>Validating API key...</span>
            </div>
          )}

          {validationError && (
            <div className="validation-status error">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
              <span>{validationError}</span>
            </div>
          )}

          {isValid && (
            <div className="validation-status valid">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
              <span>API key is valid</span>
            </div>
          )}

          <div className="free-usage-info">
            <h4>Free Usage</h4>
            {provider === 'openrouter' ? (
              <p>
                <strong>OpenRouter Free Tier:</strong> You can use VertoAI with OpenRouter's free tier for up to 10 prompts per day (shared across all users).
                For more usage, add your own OpenRouter API key to get 50+ prompts per day.
              </p>
            ) : (
              <p>You can use VertoAI with OpenRouter's free tier for up to 10 prompts per day. After that, you'll need to provide your own API key or wait for the daily limit to reset.</p>
            )}
            <button className="skip-api-key" onClick={handleClose}>
              Continue with free usage
            </button>
          </div>

          <p className="api-key-note">
            Your API key is never stored on our servers and is only passed through to the provider.
          </p>
        </div>

        <div className="modal-footer">
          <button className="modal-button secondary" onClick={handleClose}>Cancel</button>
          <button
            className="modal-button primary"
            onClick={handleSave}
            disabled={!isValid}
          >
            Save API Key
          </button>
        </div>
      </div>
    </div>
  );
};

export default ApiKeyModal;
