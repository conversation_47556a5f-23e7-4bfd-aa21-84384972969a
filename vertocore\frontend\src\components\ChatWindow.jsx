import React, { useState, useRef, useEffect } from 'react';
import ConfirmationModal from './ConfirmationModal';
import ShareModal from './ShareModal';
import { generateText } from '../services/api';
import { FREE_USAGE_LIMIT, TESTING_MODE } from '../utils/config';

const ChatWindow = ({
  selectedDomain,
  selectedProvider,
  apiKey,
  chatId,
  onUpdateChat,
  onArchiveChat,
  onDeleteChat,
  onPinChat,
  isPinned = false,
  initialMessages = []
}) => {
  const [messages, setMessages] = useState(initialMessages);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [freeUsage, setFreeUsage] = useState({
    used: 0,
    limit: FREE_USAGE_LIMIT,
    resetTime: null
  });
  const messagesEndRef = useRef(null);

  // Modal states
  const [showClearModal, setShowClearModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showArchiveModal, setShowArchiveModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [shareText, setShareText] = useState('');

  // Edit message state
  const [editingMessageId, setEditingMessageId] = useState(null);
  const [editText, setEditText] = useState('');

  // Update parent component when messages change
  // Using a ref to track if this is the initial render
  const initialRenderRef = useRef(true);
  const lastUpdateTimeRef = useRef(0);

  // Reset messages when chatId changes (new chat created) or domain changes
  useEffect(() => {
    // Always reset messages to empty array when a new chat is created
    // This ensures we don't copy the current chat when creating a new one
    if (initialMessages.length === 0) {
      setMessages([]);
    } else {
      setMessages(initialMessages);
    }
    setInput('');

    // Reset the message state completely
    initialRenderRef.current = true;
    lastMessagesLengthRef.current = 0;
    lastUpdateTimeRef.current = 0;
  }, [chatId, initialMessages, selectedDomain]);

  // Reset messages when domain changes
  useEffect(() => {
    // When domain changes, clear messages
    setMessages([]);
    setInput('');
    initialRenderRef.current = true;
    lastMessagesLengthRef.current = 0;
  }, [selectedDomain]);

  useEffect(() => {
    // Skip the first render to avoid unnecessary updates
    if (initialRenderRef.current) {
      initialRenderRef.current = false;
      return;
    }

    // Only update if we have a chat ID and messages
    if (chatId && messages.length > 0) {
      // Debounce updates to prevent too many API calls
      const now = Date.now();
      const timeSinceLastUpdate = now - lastUpdateTimeRef.current;

      // Only update if it's been at least 1 second since the last update
      // or if this is a new message (length changed)
      const isNewMessage = messages.length !== lastMessagesLengthRef.current;

      if (isNewMessage || timeSinceLastUpdate > 1000) {
        // Use setTimeout to avoid potential infinite loops
        const timeoutId = setTimeout(() => {
          lastUpdateTimeRef.current = now;
          lastMessagesLengthRef.current = messages.length;
          onUpdateChat(chatId, messages);
        }, 0);

        return () => clearTimeout(timeoutId);
      }
    }
  }, [chatId, messages, onUpdateChat]);

  // Keep track of the last messages length
  const lastMessagesLengthRef = useRef(0);

  // Load free usage data from localStorage
  useEffect(() => {
    // If in testing mode, reset usage counter to 0
    if (TESTING_MODE) {
      const resetTime = new Date();
      resetTime.setHours(resetTime.getHours() + 24);

      const resetUsage = {
        used: 0,
        limit: FREE_USAGE_LIMIT,
        resetTime: resetTime.toISOString()
      };

      setFreeUsage(resetUsage);
      localStorage.setItem('vertoai_free_usage', JSON.stringify(resetUsage));
      console.log('Testing mode enabled: Usage counter reset to 0');
      return;
    }

    const storedUsage = localStorage.getItem('vertoai_free_usage');
    if (storedUsage) {
      const parsedUsage = JSON.parse(storedUsage);

      // Check if reset time has passed
      if (parsedUsage.resetTime && new Date(parsedUsage.resetTime) < new Date()) {
        // Reset usage if the time has passed
        const newResetTime = new Date();
        newResetTime.setHours(newResetTime.getHours() + 24);

        const resetUsage = {
          used: 0,
          limit: FREE_USAGE_LIMIT,
          resetTime: newResetTime.toISOString()
        };

        setFreeUsage(resetUsage);
        localStorage.setItem('vertoai_free_usage', JSON.stringify(resetUsage));
      } else {
        setFreeUsage(parsedUsage);
      }
    } else {
      // Initialize free usage data
      const resetTime = new Date();
      resetTime.setHours(resetTime.getHours() + 24);

      const initialUsage = {
        used: 0,
        limit: FREE_USAGE_LIMIT,
        resetTime: resetTime.toISOString()
      };

      setFreeUsage(initialUsage);
      localStorage.setItem('vertoai_free_usage', JSON.stringify(initialUsage));
    }
  }, []);

  // Auto-scrolling disabled as per user request
  // Manual scroll button can be added here if needed in the future

  // Format timestamp
  const formatTime = (date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Check if user can send a message
  const canSendMessage = () => {
    // If testing mode is enabled, always allow messages
    if (TESTING_MODE) return true;

    // If user has API key, they can always send messages
    if (apiKey) return true;

    // If user doesn't have API key, check free usage
    return freeUsage.used < freeUsage.limit;
  };

  // Format time remaining until reset
  const formatTimeRemaining = () => {
    if (!freeUsage.resetTime) return 'unknown time';

    const resetTime = new Date(freeUsage.resetTime);
    const now = new Date();
    const diffMs = resetTime - now;

    if (diffMs <= 0) return 'soon';

    const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHrs > 0) {
      return `${diffHrs} hour${diffHrs !== 1 ? 's' : ''} and ${diffMins} minute${diffMins !== 1 ? 's' : ''}`;
    } else {
      return `${diffMins} minute${diffMins !== 1 ? 's' : ''}`;
    }
  };

  // Track active requests to prevent race conditions
  const activeRequestRef = useRef(null);

  // Handle sending a message
  const handleSend = async () => {
    if (input.trim() === '') return;

    // Check if user can send a message
    if (!canSendMessage()) return;

    // Store the current chat ID to ensure response goes to the correct conversation
    const currentChatIdForRequest = chatId;

    // Generate a unique request ID for this specific message
    const requestId = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    // Store this request as the active one
    activeRequestRef.current = requestId;

    // Add user message
    const userMessage = {
      id: Date.now(),
      text: input,
      sender: 'user',
      timestamp: new Date(),
      requestId: requestId, // Track which request this message belongs to
      chatId: currentChatIdForRequest // Track which chat this message belongs to
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    // Update free usage if no API key and not in testing mode
    if (!apiKey && !TESTING_MODE) {
      const updatedUsage = {
        ...freeUsage,
        used: freeUsage.used + 1
      };

      setFreeUsage(updatedUsage);
      localStorage.setItem('vertoai_free_usage', JSON.stringify(updatedUsage));
    }

    try {
      // Call the API to generate a response
      // If backend is not available, fall back to mock response
      let responseText;
      let responseMetadata = {};

      try {
        // Create a context-aware prompt that includes chat ID and conversation history
        // Format is important - the backend parses this exact format
        // The chat ID prefix is critical for maintaining separate conversation contexts

        // Include the last few messages as context (up to 5 messages)
        const contextMessages = messages.slice(-5);
        let conversationContext = "";

        if (contextMessages.length > 0) {
          conversationContext = "\n\nRecent conversation history:\n" +
            contextMessages.map(msg =>
              `${msg.sender === 'user' ? 'User' : 'AI'}: ${msg.text.substring(0, 500)}`
            ).join("\n") +
            "\n\nUser's new message: " + input.trim();
        } else {
          conversationContext = input.trim();
        }

        // Make the chat ID more prominent and unique to ensure proper context isolation
        // Also include the request ID to track this specific request
        const contextPrompt = `[Chat ID: ${currentChatIdForRequest}] [Request ID: ${requestId}] [IMPORTANT: This message belongs to conversation ${currentChatIdForRequest}] ${conversationContext}`;

        // Dynamically adjust temperature based on conversation progress
        // Start with higher temperature (more creative) for early messages
        // Then decrease temperature (more focused) once the conversation has established a topic
        let temperature = 0.7; // default temperature

        // If we have more than 3 messages, we're likely into the main topic
        // so we can reduce temperature for more focused responses
        if (messages.length >= 3) {
          // Gradually decrease temperature as conversation progresses
          temperature = Math.max(0.3, 0.7 - (messages.length * 0.05));
        }

        // For very long conversations, maintain a minimum temperature
        if (messages.length > 10) {
          temperature = 0.3;
        }

        // Only log minimal information to reduce console clutter
        console.log(`Sending request ${requestId} for chat ${currentChatIdForRequest}`);

        const response = await generateText(
          contextPrompt,
          selectedProvider,
          selectedDomain,
          apiKey,
          500,  // max tokens
          temperature,  // dynamic temperature
          requestId,    // Pass request ID to API
          currentChatIdForRequest  // Pass chat ID to API
        );

        // Check if this request is still the active one
        // If not, discard the response as the user has moved to another chat
        if (activeRequestRef.current !== requestId || chatId !== currentChatIdForRequest) {
          console.log(`Discarding response for request ${requestId} as it's no longer active`);
          return;
        }

        if (response.error) {
          // Check for rate limit error
          if (response.rate_limited || response.text.includes('Rate limit exceeded')) {
            throw new Error(`Rate limit exceeded. Please try again later or upgrade your plan.\n\n${response.text}`);
          } else {
            throw new Error(response.text || response.error);
          }
        }

        responseText = response.text;
        responseMetadata = {
          provider: response.provider,
          model: response.model,
          tokensUsed: response.tokens_used
        };
      } catch (error) {
        console.warn(`API call failed for request ${requestId}:`, error.message);

        // Check if this request is still the active one
        if (activeRequestRef.current !== requestId || chatId !== currentChatIdForRequest) {
          console.log(`Discarding error for request ${requestId} as it's no longer active`);
          return;
        }

        // Handle specific error types with user-friendly messages
        if (error.message && (error.message.includes('Rate limit exceeded') || error.message.includes('rate limit'))) {
          // Rate limit error
          responseText = `⚠️ Rate limit exceeded for OpenRouter free tier.\n\nYou can:\n1. Wait for the daily limit to reset\n2. Add your own OpenRouter API key in the settings\n\nClick the key icon in the top-right to add your API key.`;
        } else if (error.message && error.message.includes('401')) {
          // Authentication error
          responseText = `⚠️ API Authentication Failed\n\nThe OpenRouter API key appears to be invalid or expired. Please update your API key in the settings.\n\nClick the key icon in the top-right to update your API key.`;
        } else if (error.message && error.message.includes('404')) {
          // Model not found error
          responseText = `⚠️ Model Not Found\n\nThe requested model was not found. This could be because:\n1. The model name is incorrect\n2. The model is no longer available\n\nTrying a different model might help.`;
        } else {
          // Other errors
          responseText = error.message && error.message.includes('API')
            ? `Error: ${error.message}`
            : generateMockResponse(input, selectedDomain);
        }
      }

      // Final check to ensure we're still in the right chat
      if (activeRequestRef.current !== requestId || chatId !== currentChatIdForRequest) {
        console.log(`Discarding final response for request ${requestId} as it's no longer active`);
        return;
      }

      // Create AI message from response
      // Remove any square brackets that might appear at the beginning of the response
      let cleanedResponseText = responseText;
      if (cleanedResponseText && cleanedResponseText.startsWith(']')) {
        cleanedResponseText = cleanedResponseText.substring(1).trim();
      }

      const aiMessage = {
        id: Date.now(),
        text: cleanedResponseText,
        sender: 'ai',
        timestamp: new Date(),
        metadata: responseMetadata,
        requestId: requestId, // Track which request this response belongs to
        chatId: currentChatIdForRequest // Track which chat this response belongs to
      };

      // Only update messages if we're still in the same chat
      if (chatId === currentChatIdForRequest) {
        setMessages(prev => [...prev, aiMessage]);
      } else {
        console.log(`Chat ID mismatch: current=${chatId}, response for=${currentChatIdForRequest}`);
        // If we're in a different chat, we need to update the parent component directly
        // This ensures the response goes to the correct chat even if the user has switched
        onUpdateChat(currentChatIdForRequest, [...messages, aiMessage]);
      }
    } catch (error) {
      // Final check to ensure we're still in the right chat
      if (activeRequestRef.current !== requestId || chatId !== currentChatIdForRequest) {
        console.log(`Discarding error message for request ${requestId} as it's no longer active`);
        return;
      }

      // Add error message
      const errorMessage = {
        id: Date.now(),
        text: `Error: ${error.message || 'Failed to generate response. Please try again.'}`,
        sender: 'system',
        timestamp: new Date(),
        isError: true,
        requestId: requestId,
        chatId: currentChatIdForRequest
      };

      // Only update messages if we're still in the same chat
      if (chatId === currentChatIdForRequest) {
        setMessages(prev => [...prev, errorMessage]);
      } else {
        // If we're in a different chat, we need to update the parent component directly
        onUpdateChat(currentChatIdForRequest, [...messages, errorMessage]);
      }
    } finally {
      // Only update loading state if this is still the active request
      if (activeRequestRef.current === requestId) {
        setIsLoading(false);
        activeRequestRef.current = null;
      }
    }
  };

  // Handle pressing Enter to send
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  // Handle clearing the chat
  const handleClearChat = () => {
    setShowClearModal(true);
  };

  const confirmClearChat = () => {
    setMessages([]);
  };

  // Handle archiving the chat
  const handleArchiveChat = () => {
    setShowArchiveModal(true);
  };

  const confirmArchiveChat = () => {
    if (onArchiveChat) {
      onArchiveChat();
    }
  };

  // Handle pinning/unpinning the chat
  const handlePinChat = () => {
    if (onPinChat) {
      onPinChat();
    }
  };

  // Handle deleting the chat
  const handleDeleteChat = () => {
    setShowDeleteModal(true);
  };

  const confirmDeleteChat = () => {
    if (onDeleteChat) {
      onDeleteChat();
    } else {
      setMessages([]);
    }
  };

  // Handle sharing the chat
  const handleShareChat = () => {
    // Format the chat text for sharing
    const chatText = messages.map(msg => {
      const timestamp = formatTime(msg.timestamp);
      return `[${timestamp}] ${msg.sender === 'user' ? 'You' : 'AI'}: ${msg.text}`;
    }).join('\n\n');

    // Set the share text and open the modal
    setShareText(chatText);
    setShowShareModal(true);
  };

  // Handle editing a message
  const handleEditMessage = (messageId, currentText) => {
    setEditingMessageId(messageId);
    setEditText(currentText);
  };

  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditText('');
  };

  // Handle saving edited message
  const handleSaveEdit = async () => {
    if (editText.trim() === '') return;

    // Find the message being edited
    const messageIndex = messages.findIndex(msg => msg.id === editingMessageId);
    if (messageIndex === -1) return;

    // Update the message text
    const updatedMessages = [...messages];
    updatedMessages[messageIndex] = {
      ...updatedMessages[messageIndex],
      text: editText.trim(),
      edited: true,
      editedAt: new Date()
    };

    // Remove all AI responses after the edited message
    const messagesToKeep = updatedMessages.slice(0, messageIndex + 1);
    setMessages(messagesToKeep);

    // Clear edit state
    setEditingMessageId(null);
    setEditText('');

    // Generate new AI response for the edited message
    if (!canSendMessage()) return;

    setIsLoading(true);

    try {
      // Create context for the edited message
      const contextMessages = messagesToKeep.slice(-5);
      let conversationContext = "";

      if (contextMessages.length > 1) {
        conversationContext = "\n\nRecent conversation history:\n" +
          contextMessages.slice(0, -1).map(msg =>
            `${msg.sender === 'user' ? 'User' : 'AI'}: ${msg.text.substring(0, 500)}`
          ).join("\n") +
          "\n\nUser's edited message: " + editText.trim();
      } else {
        conversationContext = editText.trim();
      }

      const requestId = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
      const contextPrompt = `[Chat ID: ${chatId}] [Request ID: ${requestId}] [EDITED MESSAGE] ${conversationContext}`;

      const response = await generateText(
        contextPrompt,
        selectedProvider,
        selectedDomain,
        apiKey,
        500,
        0.7,
        requestId,
        chatId
      );

      if (response.error) {
        throw new Error(response.text || response.error);
      }

      let cleanedResponseText = response.text;
      if (cleanedResponseText && cleanedResponseText.startsWith(']')) {
        cleanedResponseText = cleanedResponseText.substring(1).trim();
      }

      const aiMessage = {
        id: Date.now(),
        text: cleanedResponseText,
        sender: 'ai',
        timestamp: new Date(),
        metadata: {
          provider: response.provider,
          model: response.model,
          tokensUsed: response.tokens_used
        },
        requestId: requestId,
        chatId: chatId
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      const errorMessage = {
        id: Date.now(),
        text: `Error: ${error.message || 'Failed to generate response. Please try again.'}`,
        sender: 'system',
        timestamp: new Date(),
        isError: true
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Enter key in edit textarea
  const handleEditKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveEdit();
    }
  };

  // Generate a mock response based on domain
  const generateMockResponse = (userInput, domain) => {
    // More robust check for nonsensical input
    const trimmedInput = userInput.trim().toLowerCase();

    // Handle greetings with friendly responses
    if (trimmedInput === 'hi' || trimmedInput === 'hello' || trimmedInput === 'hey') {
      return `Hello! How can I help you with ${domain} today?`;
    }

    // Check for gibberish, very short inputs, or random characters
    if (trimmedInput.length < 5 ||
        /^[a-z]{1,8}$/i.test(trimmedInput) ||
        /^[a-z0-9]{1,10}$/i.test(trimmedInput) ||
        /^[^a-zA-Z0-9]{1,5}$/.test(trimmedInput)) {
      return `I'd love to help you with ${domain}. Could you please ask a more specific question?`;
    }

    const domainResponses = {
      fintech: [
        "I'd recommend diversifying your portfolio with more tech stocks to balance risk exposure.",
        "Given current market volatility, a cautious investment approach would be prudent this quarter.",
        "SEC regulations require reporting this transaction within 48 hours."
      ],
      healthcare: [
        "Based on your description, consult with a specialist for proper diagnosis of these symptoms.",
        "Recent research shows this treatment has been promising for similar cases.",
        "The data indicates a correlation between this medication and the reported side effects."
      ],
      education: [
        "This curriculum aligns well with modern teaching approaches for this age group.",
        "Consider incorporating more project-based evaluations to improve your assessment methodology.",
        "Research shows this learning approach improves retention by approximately 27%."
      ],
      law: [
        "The Smith v. Jones (2018) case sets a relevant precedent for your situation.",
        "This contract clause contains ambiguities that could lead to future disputes.",
        "Current regulations indicate compliance risks in reporting and disclosure requirements."
      ]
    };

    // Select a random response from the appropriate domain
    const responses = domainResponses[domain] || domainResponses.fintech;
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

    return randomResponse;
  };

  return (
    <div className="chat-container">
      {/* Confirmation Modals */}
      <ConfirmationModal
        isOpen={showClearModal}
        onClose={() => setShowClearModal(false)}
        onConfirm={confirmClearChat}
        title="Clear Conversation"
        message="Are you sure you want to clear all messages? This will remove all messages from the current conversation."
        confirmText="Clear"
        type="danger"
      />

      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={confirmDeleteChat}
        title="Delete Chat"
        message="Are you sure you want to delete this chat? This action cannot be undone."
        confirmText="Delete"
        type="danger"
      />

      <ConfirmationModal
        isOpen={showArchiveModal}
        onClose={() => setShowArchiveModal(false)}
        onConfirm={confirmArchiveChat}
        title="Archive Chat"
        message={isPinned ?
          "This chat is currently pinned. Archiving will unpin it and move it to the archive folder. Do you want to continue?" :
          "Do you want to archive this chat? You can access archived chats from your archive folder."}
        confirmText="Archive"
      />

      {/* Share Modal */}
      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        chatText={shareText}
        messages={messages}
      />

      <div className="chat-header">
        <div className="chat-domain-indicator">
          <div
            className={`domain-icon domain-${selectedDomain}`}
            style={{
              background: selectedDomain === 'fintech' ? 'linear-gradient(135deg, #4caf50, #2e7d32)' :
                         selectedDomain === 'healthcare' ? 'linear-gradient(135deg, #2196f3, #0d47a1)' :
                         selectedDomain === 'education' ? 'linear-gradient(135deg, #ff9800, #e65100)' :
                         selectedDomain === 'law' ? 'linear-gradient(135deg, #9c27b0, #4a148c)' :
                         'linear-gradient(135deg, var(--gradient-start), var(--gradient-end))'
            }}
          >
            {selectedDomain === 'fintech' && (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="1" x2="12" y2="23"></line>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            )}
            {selectedDomain === 'healthcare' && (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
              </svg>
            )}
            {selectedDomain === 'education' && (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
              </svg>
            )}
            {selectedDomain === 'law' && (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 22v-5"></path>
                <path d="M9 8h6"></path>
                <path d="M20 22H4"></path>
                <path d="M6 8v10"></path>
                <path d="M18 8v10"></path>
                <path d="M12 2L2 8h20L12 2z"></path>
              </svg>
            )}
          </div>
          <span className="domain-name">{selectedDomain.charAt(0).toUpperCase() + selectedDomain.slice(1)}</span>
        </div>

        <div className="chat-provider-indicator">
          <span className="provider-name">{selectedProvider.toUpperCase()}</span>
          <div className={`provider-status ${apiKey ? 'connected' : (selectedProvider === 'openrouter' ? 'free-tier' : 'disconnected')}`}>
            {apiKey ? 'Connected' : (selectedProvider === 'openrouter' ? 'Free Tier' : 'No API Key')}
          </div>
        </div>

        <div className="chat-actions">
          <button
            className={`chat-action-button ${isPinned ? 'active' : ''}`}
            onClick={handlePinChat}
            title={isPinned ? "Unpin chat" : "Pin chat"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              <line x1="9" y1="9" x2="15" y2="9"></line>
              <line x1="9" y1="13" x2="15" y2="13"></line>
            </svg>
          </button>

          <button
            className="chat-action-button"
            onClick={handleArchiveChat}
            title="Archive chat"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="21 8 21 21 3 21 3 8"></polyline>
              <rect x="1" y="3" width="22" height="5"></rect>
              <line x1="10" y1="12" x2="14" y2="12"></line>
            </svg>
          </button>

          <button
            className="chat-action-button"
            onClick={handleShareChat}
            title="Share chat"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="18" cy="5" r="3"></circle>
              <circle cx="6" cy="12" r="3"></circle>
              <circle cx="18" cy="19" r="3"></circle>
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
            </svg>
          </button>

          <button
            className="chat-action-button delete"
            onClick={handleDeleteChat}
            title="Delete chat"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="3 6 5 6 21 6"></polyline>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            </svg>
          </button>
        </div>
      </div>

      <div className="chat-messages">
        {messages.length === 0 ? (
          <div className="chat-empty-state">
            <div className="empty-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                <path d="M8 9h8"></path>
                <path d="M8 13h6"></path>
              </svg>
            </div>
            <h3>Start a conversation</h3>
            <p>Select a domain and provider, then start chatting!</p>
            {!apiKey && (
              <div className="free-usage-reminder">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                </svg>
                {TESTING_MODE ? (
                  <span>Testing mode enabled: Usage limits are disabled</span>
                ) : (
                  <span>Using free tier: {freeUsage.used}/{freeUsage.limit} prompts used today</span>
                )}
              </div>
            )}
          </div>
        ) : (
          messages.map(message => (
            <div
              key={message.id}
              className={`message message-${message.sender}`}
            >
              <div className="message-content">
                {editingMessageId === message.id ? (
                  // Edit mode
                  <div className="message-edit-container">
                    <textarea
                      className="message-edit-textarea"
                      value={editText}
                      onChange={(e) => setEditText(e.target.value)}
                      onKeyDown={handleEditKeyDown}
                      placeholder="Edit your message..."
                      autoFocus
                    />
                    <div className="message-edit-buttons">
                      <button
                        className="message-edit-button message-edit-send"
                        onClick={handleSaveEdit}
                        disabled={editText.trim() === ''}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="22" y1="2" x2="11" y2="13"></line>
                          <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                        </svg>
                        Send
                      </button>
                      <button
                        className="message-edit-button message-edit-cancel"
                        onClick={handleCancelEdit}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  // Normal display mode
                  <>
                    {message.sender === 'user' && (
                      <button
                        className="message-edit-icon"
                        onClick={() => handleEditMessage(message.id, message.text)}
                        title="Edit message"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                      </button>
                    )}
                    <div className="message-bubble">
                      {message.text}
                    </div>
                    <div className="message-time">
                      {formatTime(message.timestamp)}
                      {message.edited && <span className="message-edited"> (edited)</span>}
                    </div>
                  </>
                )}
              </div>
            </div>
          ))
        )}

        {isLoading && (
          <div className="message message-ai">
            <div className="message-content">
              <div className="message-bubble message-loading">
                <div className="loading-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <div className="chat-input-container">
        <div className="chat-input-wrapper">
          <textarea
            className="chat-input"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={TESTING_MODE ? "Testing mode: Type your message here..." :
              (canSendMessage() ? "Type your message here..." : "Free usage limit reached. Please add an API key...")}
            disabled={!canSendMessage() || isLoading}
          />

          <div className="chat-input-buttons">
            {messages.length > 0 && (
              <button
                className="clear-button"
                onClick={handleClearChat}
                title="Clear conversation"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
              </button>
            )}

            <button
              className="send-button"
              onClick={handleSend}
              disabled={!canSendMessage() || input.trim() === '' || isLoading}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="22" y1="2" x2="11" y2="13"></line>
                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
              </svg>
              <span className="send-text">Send</span>
            </button>
          </div>
        </div>

        <div className="chat-input-footer">
          <div className="input-info">
            {TESTING_MODE ? (
              <span className="input-status connected">
                <span className="status-dot"></span>
                Testing mode: Unlimited usage
              </span>
            ) : apiKey ? (
              <span className="input-status connected">
                <span className="status-dot"></span>
                Ready to chat
              </span>
            ) : freeUsage.used < freeUsage.limit ? (
              <span className="input-status free-usage">
                <span className="status-dot"></span>
                Free usage: {freeUsage.used}/{freeUsage.limit} prompts used
              </span>
            ) : (
              <span className="input-status disconnected">
                <span className="status-dot"></span>
                Free limit reached. Resets in {formatTimeRemaining()}
              </span>
            )}
          </div>

          <div className="domain-badge">
            {selectedDomain.charAt(0).toUpperCase() + selectedDomain.slice(1)} + {selectedProvider.toUpperCase()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatWindow;
